# 📐🧪⚛️ MathCapture Studio - Tabbed Interface Guide

## 🎯 **NEW: 3 Subject-Specific Tabs**

Your MathCapture Studio now features **dedicated tabs** for different scientific disciplines!

---

## 📋 **Tab Overview:**

### **📐 Mathematics Tab**
- **Focus**: Mathematical equations, formulas, and expressions
- **Features**: 
  - Advanced LaTeX processing
  - Mathematical symbol recognition
  - Equation solving and formatting
  - Calculus, algebra, geometry support

### **🧪 Chemistry Tab**
- **Focus**: Chemical equations, molecular formulas, and reactions
- **Features**:
  - Chemical equation balancing
  - Molecular structure recognition
  - Reaction mechanism processing
  - Chemical notation optimization

### **⚛️ Physics Tab**
- **Focus**: Physical laws, equations, and scientific notation
- **Features**:
  - Physics formula recognition
  - Unit conversion support
  - Scientific notation handling
  - Physical constant integration

---

## 🔄 **How the Tabs Work:**

### **Shared Components:**
- **📁 File Management**: Import PDFs/images once, use across all tabs
- **📷 Preview Window**: Shared image viewing and region selection
- **🔧 LaTeX-OCR Engine**: Same powerful OCR for all subjects

### **Subject-Specific Components:**
- **📝 Separate Editors**: Each tab has its own LaTeX editor
- **📊 Individual Queues**: Separate equation queues per subject
- **🎯 Specialized Processing**: Subject-aware LaTeX optimization
- **📤 Independent Export**: Export equations by subject

---

## 🚀 **Using the Tabbed Interface:**

### **Step 1: Import Files (Any Tab)**
1. Click **"Import PDF/Images"** in any tab
2. Files are shared across all tabs
3. Switch between tabs while keeping the same files

### **Step 2: Select Subject Tab**
1. Click on **📐 Mathematics**, **🧪 Chemistry**, or **⚛️ Physics**
2. Window title updates to show current subject
3. Editor and queue become subject-specific

### **Step 3: Process Equations**
1. Select region in the shared preview
2. Click **"Process [Subject] OCR"**
3. Results appear in the current subject's editor
4. LaTeX is optimized for the specific subject

### **Step 4: Manage Subject Queues**
1. Add equations to subject-specific queues
2. Each subject maintains its own equation list
3. Export or copy equations by subject

---

## 🎯 **Subject-Specific Features:**

### **Mathematics Tab Features:**
- ✅ **Advanced calculus** notation
- ✅ **Matrix and vector** processing
- ✅ **Complex number** handling
- ✅ **Geometric formula** recognition
- ✅ **Statistical equation** support

### **Chemistry Tab Features:**
- ✅ **Chemical equation** balancing
- ✅ **Molecular formula** optimization
- ✅ **Reaction arrow** formatting
- ✅ **Chemical bond** notation
- ✅ **Stoichiometry** calculations

### **Physics Tab Features:**
- ✅ **Physical law** formatting
- ✅ **Unit notation** standardization
- ✅ **Scientific notation** handling
- ✅ **Vector and scalar** distinction
- ✅ **Quantum mechanics** symbols

---

## 📊 **Queue Management:**

### **Separate Queues Per Subject:**
- **Mathematics Queue**: Pure math equations
- **Chemistry Queue**: Chemical formulas and reactions
- **Physics Queue**: Physical laws and constants

### **Queue Operations:**
- **Add to Queue**: Subject-specific addition
- **Clear Queue**: Clear individual subject queues
- **Export Subject**: Export only selected subject equations
- **Copy All**: Copy all equations from one subject

---

## 🔧 **Advanced Features:**

### **Smart Subject Detection:**
- **Auto-optimization** based on current tab
- **Context-aware** LaTeX processing
- **Subject-specific** error correction
- **Intelligent** symbol recognition

### **Cross-Tab Functionality:**
- **Shared file management** across all tabs
- **Unified preview** window
- **Consistent** LaTeX-OCR processing
- **Integrated** export options

---

## 📤 **Export Options:**

### **By Subject:**
```
📐 Mathematics Export → Math equations only
🧪 Chemistry Export → Chemical equations only  
⚛️ Physics Export → Physics formulas only
```

### **Combined Export:**
- Export all subjects together
- Organized by subject sections
- Professional document formatting

---

## 🎉 **Benefits:**

### **Organization:**
- ✅ **Separate** equations by subject
- ✅ **Focused** workflow per discipline
- ✅ **Clean** interface organization
- ✅ **Efficient** project management

### **Productivity:**
- ✅ **Subject-specific** optimization
- ✅ **Faster** equation processing
- ✅ **Better** LaTeX quality
- ✅ **Streamlined** workflow

### **Professional Output:**
- ✅ **Subject-appropriate** formatting
- ✅ **Discipline-specific** notation
- ✅ **Publication-ready** quality
- ✅ **Academic** standards compliance

---

## 🚀 **Getting Started:**

1. **Launch** MathCapture Studio
2. **Choose** your subject tab (📐🧪⚛️)
3. **Import** your documents
4. **Process** equations with subject-specific optimization
5. **Export** professional results!

**Your scientific workflow is now perfectly organized by discipline!** 🎯✨
